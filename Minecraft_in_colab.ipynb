# @title Minecraft server in colab
# @markdown ---

# @markdown WELCOME TO THE MINECRAFT SERVER SETUP EXTRAVAGANZA!

# @markdown ---
# @markdown ❤️ Created by [OEvortex](https://youtube.com/@OEvortex).

# @markdown Please subscribe to my channel.

# @markdown ---
# @markdown  First things first, let's pick the version of the extravaganza. It's like choosing the theme of the party!
# @markdown  We've got all these versions lined up for you. Choose wisely, my friend!

version = '1.20.2'  # @param ["1.20.2", "1.20.1", "1.20", "1.19.4", "1.19.3", "1.19.2", "1.19.1", "1.19", "1.18.2", "1.18.1", "1.18", "1.17.1", "1.17", "1.16.5", "1.16.4", "1.16.3", "1.15.2", "1.14.4", "1.13.2", "1.12.2", "1.11.2", "1.10.2", "1.9.4", "1.8.8"]
# @markdown ---


# @markdown Now, it's time to choose the type of your extravaganza. It's like choosing the music for the party!

server_type = 'paper'  # @param ["paper", "forge", "fabric"]
# @markdown ---

# Alright, let's invite Google Drive to the extravaganza. We're going to need a place to store all the party decorations.

import requests
import json
import os
import subprocess
import urllib.request
import shutil

MINECRAFT_SERVER_DIR = './Minecraft-server'
MINECRAFT_SERVER_JAR = os.path.join(MINECRAFT_SERVER_DIR, 'server.jar')
MINECRAFT_SERVER_URL = 'https://launcher.mojang.com/v1/objects/fe3aa5b7e1b7b6b1e5e2e2e2e2e2e2e2e2e2e2/server.jar'  # Example URL, replace with latest if needed

# Create the directory which will be used for the server
os.makedirs(MINECRAFT_SERVER_DIR, exist_ok=True)

# Internal init...

a = requests.get("https://papermc.io/api/v2/projects/paper/versions/" + version)
#print(a.json()["builds"][-1])
b = requests.get("https://papermc.io/api/v2/projects/paper/versions/" + version + "/builds/" + str(a.json()["builds"][-1]))
#print(b.json()["downloads"]["application"]["name"])
print("https://papermc.io/api/v2/projects/paper/versions/" + version + "/builds/" + str(a.json()["builds"][-1]) + "/downloads/" + b.json()["downloads"]["application"]["name"])

paperURL = "https://papermc.io/api/v2/projects/paper/versions/" + version + "/builds/" + str(a.json()["builds"][-1]) + "/downloads/" + b.json()["downloads"]["application"]["name"]

forgeURL = "https://serverjars.com/api/fetchJar/modded/forge/" + version

jar_name = {'paper': 'server.jar', 'fabric': 'fabric-installer.jar', 'forge': 'forge-installer.jar'}
url = {
    'paper': (paperURL),
    'fabric': 'https://maven.fabricmc.net/net/fabricmc/fabric-installer/0.7.4/fabric-installer-0.7.4.jar',
    'forge': (forgeURL)
    }

print('Downloading to Google Drive...')

r = requests.get(url[server_type])

if r.status_code == 200:
  with open('/content/drive/My Drive/Minecraft-server/' + jar_name[server_type], 'wb') as f:
    f.write(r.content)
else:
  print('Error '+ str(r.status_code) + '! Most likely you entered an unsupported version. Try running the code again if you think that shouldn\'t have happened.')

# Running specific install path.
if server_type == 'fabric':
  !java -jar fabric-installer.jar server -mcversion $version -downloadMinecraft

if server_type == 'forge':
  %cd "/content/drive/My Drive/Minecraft-server"
  !java -jar forge-installer.jar --installServer

# Saving config
colabconfig = {"server_type": server_type,
               "server_version": version}
json.dump(colabconfig, open("colabconfig.json",'w'))

print('Completed!')  # todo: Would show even after erroring.

# Please read the file stored in your server folder before running this command.
# Also, go to https://www.minecraft.net/en-us/eula to read Minecraft's EULA.

# Accept EULA
with open(os.path.join(MINECRAFT_SERVER_DIR, 'eula.txt'), 'w') as f:
    f.write('eula=true\n')

# Run the server (first run will generate config files)
print('Starting Minecraft server...')
subprocess.run(['java', '-Xmx1024M', '-Xms1024M', '-jar', 'server.jar', 'nogui'], cwd=MINECRAFT_SERVER_DIR)


# @title Minecraft server settings
# Import necessary libraries

# Define the path to the server.properties file
file_path = './Minecraft-server/server.properties'

# Open the server.properties file in read mode
with open(file_path, 'r') as file:
    # Read the contents of the file
    lines = file.readlines()

# Create a dictionary to store the properties
properties = {}

# Parse the properties from the file
for line in lines:
    if line.strip() and not line.startswith('#'):
        key, value = line.strip().split('=')
        properties[key] = value

# Now you can make changes to the properties. For example:
properties['difficulty'] = 'easy'  #  ["peaceful", "easy", "normal", "hard"]
properties['gamemode'] = 'survival'  # ["survival", "creative", "adventure", "spectator"]
properties['allow-flight'] = False
properties['white-list'] = False
properties['spawn-monsters'] = True
properties['spawn-animals'] = True
properties['enable-command-block'] = True
properties['motd'] = 'A Minecraft Server'
# do online mode false to make server cracked
properties['online-mode'] = False
properties['pvp'] = True

# Open the server.properties file in write mode
with open(file_path, 'w') as file:
    # Write the modified properties back to the file
    for key, value in properties.items():
        file.write(f'{key}={value}\n')

print('Changes saved!')

# (Optional) Clean up Minecraft server files
import shutil
import os

MINECRAFT_SERVER_DIR = './Minecraft-server'

# Uncomment the following lines to delete the server directory and all its contents
# if os.path.exists(MINECRAFT_SERVER_DIR):
#     shutil.rmtree(MINECRAFT_SERVER_DIR)
#     print('Minecraft server directory removed.')


import os
import re
import json
import glob

# Update the package lists
!sudo apt update &>/dev/null && echo "apt cache successfully updated" || echo "apt cache update failed, you might receive stale packages"

# Mount Google Drive
from google.colab import drive
drive.mount('/content/drive')
# Change directory to the Minecraft server folder on Google Drive
%cd "/content/drive/My Drive/Minecraft-server"
!ls #list the directory contents (to verify that working directory was changed)

# Import config file.
if os.path.isfile("colabconfig.json"):
  colabconfig = json.load(open("colabconfig.json"))
else:
  colabconfig = {"server_type": "generic"} # using default, if config doesn't exist.
  json.dump(colabconfig, open("colabconfig.json",'w'))

# Install OpenJDK 17
# !wget -qO - https://adoptopenjdk.jfrog.io/adoptopenjdk/api/gpg/key/public | sudo apt-key add -
# !sudo add-apt-repository --yes https://adoptopenjdk.jfrog.io/adoptopenjdk/deb/ &>/dev/null || echo "Failed to add repo. Still can be ignored if openjdk17 gets installed."
if colabconfig["server_type"] == "forge":
   version = colabconfig["server_version"]
   if colabconfig["server_version"] < "1.17":
    !sudo apt-get install openjdk-15-jre-headless &>/dev/null && echo "Yay! Openjdk15 has been successfully installed." || echo "Failed to install OpenJdk15."
   else:
    !sudo apt-get install openjdk-17-jre-headless &>/dev/null && echo "Yay! Openjdk17 has been successfully installed." || echo "Failed to install OpenJdk17."
else:
  !sudo apt-get install openjdk-17-jre-headless &>/dev/null && echo "Yay! Openjdk17 has been successfully installed." || echo "Failed to install OpenJdk17."

#Perform java version check
java_ver = !java -version 2>&1 | awk -F[\"\.] -v OFS=. 'NR==1{print $2}'
if java_ver[0] == "17" :
  print("Openjdk17 is working correctly, you are good to go.")
else:
  print("Openjdk17 doesn't seem to be installed or isn't working, falling back to java", java_ver[0], ". You might experience reduced performance. Minecraft 1.17 and above might fail to launch.")

# Server jar names.
jar_list = {'paper': 'server.jar', 'fabric': 'fabric-server-launch.jar', 'generic': 'server.jar', 'forge': 'forge.jar'}
jar_name = jar_list[colabconfig["server_type"]]

# Java arguments.
if colabconfig["server_type"] == "paper":
  server_flags = "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC -XX:+AlwaysPreTouch -XX:G1NewSizePercent=30 -XX:G1MaxNewSizePercent=40 -XX:G1HeapRegionSize=8M -XX:G1ReservePercent=20 -XX:G1HeapWastePercent=5 -XX:G1MixedGCCountTarget=4 -XX:InitiatingHeapOccupancyPercent=15 -XX:G1MixedGCLiveThresholdPercent=90 -XX:G1RSetUpdatingPauseTimePercent=5 -XX:SurvivorRatio=32 -XX:+PerfDisableSharedMem -XX:MaxTenuringThreshold=1 -Dusing.aikars.flags=https://mcflags.emc.gs -Daikars.new.flags=true"
else:
  server_flags = "" # aiker's flags might negatively impact performance on non-paper servers.
memory_allocation = "-Xms8704M -Xmx8704M"

# Choose the tunnel service you want to use
# Available options: ngrok, argo, playit
tunnel_service = "playit"
print("Using", tunnel_service)

!java $memory_allocation $server_flags -jar $jar_name nogui

